#!/usr/bin/env python3
"""
Script to build Windows app with specific flavor configuration
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

# Flavor configurations
FLAVORS = {
    'gp': {
        'app_name': 'GP Stock',
        'company_name': 'GP Stock Inc.',
        'internal_name': 'gp_stock',
        'executable_name': 'gp_stock',
        'icon_path': 'gp\\icon.ico'
    },
    'yhxt': {
        'app_name': '沅和信投',
        'company_name': 'YHXT Inc.',
        'internal_name': 'yhxt_stock',
        'executable_name': 'yhxt_stock',
        'icon_path': 'yhxt\\icon.ico'
    },
    'bszb': {
        'app_name': '宝石资本',
        'company_name': 'Gemstone Capital Inc.',
        'internal_name': 'bszb_capital',
        'executable_name': 'bszb_capital',
        'icon_path': 'bszb\\icon.ico'
    },
    'dyzb': {
        'app_name': '德盈资本',
        'company_name': 'Deying Capital Inc.',
        'internal_name': 'dyzb_capital',
        'executable_name': 'dyzb_capital',
        'icon_path': 'dyzb\\icon.ico'
    }
}

def backup_original_files():
    """Backup original files before modification"""
    files_to_backup = [
        'windows/CMakeLists.txt',
        'windows/runner/CMakeLists.txt',
        'windows/runner/Runner.rc'
    ]
    
    for file_path in files_to_backup:
        if Path(file_path).exists():
            backup_path = f"{file_path}.backup"
            if not Path(backup_path).exists():
                shutil.copy(file_path, backup_path)
                print(f"Backed up {file_path} to {backup_path}")

def restore_original_files():
    """Restore original files from backup"""
    files_to_restore = [
        'windows/CMakeLists.txt',
        'windows/runner/CMakeLists.txt',
        'windows/runner/Runner.rc'
    ]
    
    for file_path in files_to_restore:
        backup_path = f"{file_path}.backup"
        if Path(backup_path).exists():
            shutil.copy(backup_path, file_path)
            print(f"Restored {file_path} from backup")

def setup_flavor_files(flavor, config):
    """Setup flavor-specific files for build"""
    print(f"Setting up files for flavor: {flavor}")
    
    # Copy flavor-specific CMakeLists.txt files
    flavor_cmake = f'windows/CMakeLists_{flavor}.txt'
    flavor_runner_cmake = f'windows/runner/CMakeLists_{flavor}.txt'
    flavor_rc = f'windows/runner/Runner_{flavor}.rc'
    
    if Path(flavor_cmake).exists():
        shutil.copy(flavor_cmake, 'windows/CMakeLists.txt')
        print(f"Using {flavor_cmake}")
    
    if Path(flavor_runner_cmake).exists():
        shutil.copy(flavor_runner_cmake, 'windows/runner/CMakeLists.txt')
        print(f"Using {flavor_runner_cmake}")
    
    if Path(flavor_rc).exists():
        shutil.copy(flavor_rc, 'windows/runner/Runner.rc')
        print(f"Using {flavor_rc}")

def build_flutter_app(flavor, mode='release', channel='prod'):
    """Build Flutter app with specified flavor"""
    print(f"Building Flutter app for flavor: {flavor}, mode: {mode}, channel: {channel}")
    
    cmd = [
        'flutter', 'build', 'windows',
        '--dart-define', f'FLAVOR={flavor}',
        '--dart-define', f'CHANNEL={channel}'
    ]
    
    if mode == 'release':
        cmd.append('--release')
    elif mode == 'debug':
        cmd.append('--debug')
    elif mode == 'profile':
        cmd.append('--profile')
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("Build successful!")
        print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"Build failed: {e}")
        print(f"Error output: {e.stderr}")
        return False

def main():
    """Main function"""
    if len(sys.argv) < 2:
        print("Usage: python build_windows_flavor.py <flavor> [mode] [channel]")
        print("Available flavors:", ', '.join(FLAVORS.keys()))
        print("Available modes: debug, profile, release (default: release)")
        print("Available channels: test, pre, prod (default: prod)")
        sys.exit(1)
    
    flavor = sys.argv[1]
    mode = sys.argv[2] if len(sys.argv) > 2 else 'release'
    channel = sys.argv[3] if len(sys.argv) > 3 else 'prod'
    
    if flavor not in FLAVORS:
        print(f"Invalid flavor: {flavor}")
        print("Available flavors:", ', '.join(FLAVORS.keys()))
        sys.exit(1)
    
    config = FLAVORS[flavor]
    
    try:
        # Backup original files
        backup_original_files()
        
        # Setup flavor-specific files
        setup_flavor_files(flavor, config)
        
        # Build the app
        success = build_flutter_app(flavor, mode, channel)
        
        if success:
            print(f"\n✅ Successfully built {config['app_name']} for Windows!")
            print(f"Executable: build/windows/runner/{mode}/{config['executable_name']}.exe")
        else:
            print(f"\n❌ Failed to build {config['app_name']} for Windows!")
            
    finally:
        # Restore original files
        restore_original_files()

if __name__ == '__main__':
    main()
