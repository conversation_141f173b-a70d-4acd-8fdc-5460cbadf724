#!/usr/bin/env python3
"""
Script to generate Windows flavor-specific resource files and CMakeLists.txt
"""

import os
import shutil
import yaml
from pathlib import Path

# Flavor configurations
FLAVORS = {
    'gp': {
        'app_name': 'GP Stock',
        'company_name': 'GP Stock Inc.',
        'internal_name': 'gp_stock',
        'executable_name': 'gp_stock',
        'icon_path': 'gp\\icon.ico'
    },
    'yhxt': {
        'app_name': '沅和信投',
        'company_name': 'YHXT Inc.',
        'internal_name': 'yhxt_stock',
        'executable_name': 'yhxt_stock',
        'icon_path': 'yhxt\\icon.ico'
    },
    'bszb': {
        'app_name': '宝石资本',
        'company_name': 'Gemstone Capital Inc.',
        'internal_name': 'bszb_capital',
        'executable_name': 'bszb_capital',
        'icon_path': 'bszb\\icon.ico'
    },
    'dyzb': {
        'app_name': '德盈资本',
        'company_name': 'Deying Capital Inc.',
        'internal_name': 'dyzb_capital',
        'executable_name': 'dyzb_capital',
        'icon_path': 'dyzb\\icon.ico'
    }
}

def generate_resource_file(flavor, config):
    """Generate flavor-specific Runner.rc file"""
    template_path = Path('windows/runner/Runner_template.rc')
    output_path = Path(f'windows/runner/Runner_{flavor}.rc')
    
    if not template_path.exists():
        print(f"Template file {template_path} not found!")
        return False
    
    with open(template_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Replace placeholders
    content = content.replace('{{FLAVOR_ICON_PATH}}', config['icon_path'])
    content = content.replace('{{APP_NAME}}', config['app_name'])
    content = content.replace('{{COMPANY_NAME}}', config['company_name'])
    content = content.replace('{{INTERNAL_NAME}}', config['internal_name'])
    content = content.replace('{{EXECUTABLE_NAME}}', config['executable_name'])
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Generated {output_path}")
    return True

def generate_cmake_file(flavor, config):
    """Generate flavor-specific CMakeLists.txt"""
    template_path = Path('windows/CMakeLists_template.txt')
    output_path = Path(f'windows/CMakeLists_{flavor}.txt')
    
    # If template doesn't exist, create it from the current CMakeLists.txt
    if not template_path.exists():
        shutil.copy('windows/CMakeLists.txt', template_path)
        print(f"Created template {template_path}")
    
    with open(template_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Replace binary name
    content = content.replace('set(BINARY_NAME "gp_h5")', f'set(BINARY_NAME "{config["executable_name"]}")')
    content = content.replace('project(gp_h5 LANGUAGES CXX)', f'project({config["internal_name"]} LANGUAGES CXX)')
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Generated {output_path}")
    return True

def generate_runner_cmake(flavor, config):
    """Generate flavor-specific runner CMakeLists.txt"""
    template_path = Path('windows/runner/CMakeLists_template.txt')
    output_path = Path(f'windows/runner/CMakeLists_{flavor}.txt')
    
    # If template doesn't exist, create it from the current CMakeLists.txt
    if not template_path.exists():
        shutil.copy('windows/runner/CMakeLists.txt', template_path)
        print(f"Created template {template_path}")
    
    with open(template_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Replace Runner.rc with flavor-specific file
    content = content.replace('"Runner.rc"', f'"Runner_{flavor}.rc"')
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Generated {output_path}")
    return True

def main():
    """Main function to generate all flavor files"""
    print("Generating Windows flavor-specific files...")
    
    # Create scripts directory if it doesn't exist
    os.makedirs('scripts', exist_ok=True)
    
    for flavor, config in FLAVORS.items():
        print(f"\nGenerating files for flavor: {flavor}")
        
        # Generate resource file
        if not generate_resource_file(flavor, config):
            continue
        
        # Generate CMakeLists files
        generate_cmake_file(flavor, config)
        generate_runner_cmake(flavor, config)
    
    print("\nAll flavor files generated successfully!")
    print("\nTo build a specific flavor, use:")
    for flavor in FLAVORS.keys():
        print(f"  flutter build windows --flavor {flavor}")

if __name__ == '__main__':
    main()
