#!/bin/bash

# Shell script to build Flutter app with specific flavor
# Usage: ./build_flavor.sh <flavor> [mode] [channel]

if [ $# -eq 0 ]; then
    echo "Usage: ./build_flavor.sh <flavor> [mode] [channel]"
    echo "Available flavors: gp, yhxt, bszb, dyzb"
    echo "Available modes: debug, profile, release (default: release)"
    echo "Available channels: test, pre, prod (default: prod)"
    exit 1
fi

FLAVOR=$1
MODE=${2:-release}
CHANNEL=${3:-prod}

# Validate flavor
case $FLAVOR in
    gp|yhxt|bszb|dyzb)
        ;;
    *)
        echo "Invalid flavor: $FLAVOR"
        echo "Available flavors: gp, yhxt, bszb, dyzb"
        exit 1
        ;;
esac

echo "Building Flutter app for flavor: $FLAVOR, mode: $MODE, channel: $CHANNEL"

# Backup original files
if [ ! -f "windows/CMakeLists.txt.backup" ]; then
    cp windows/CMakeLists.txt windows/CMakeLists.txt.backup
    cp windows/runner/CMakeLists.txt windows/runner/CMakeLists.txt.backup
    cp windows/runner/Runner.rc windows/runner/Runner.rc.backup
    echo "Backed up original files"
fi

# Copy flavor-specific files
if [ -f "windows/CMakeLists_${FLAVOR}.txt" ]; then
    cp "windows/CMakeLists_${FLAVOR}.txt" windows/CMakeLists.txt
    echo "Using CMakeLists_${FLAVOR}.txt"
fi

if [ -f "windows/runner/CMakeLists_${FLAVOR}.txt" ]; then
    cp "windows/runner/CMakeLists_${FLAVOR}.txt" windows/runner/CMakeLists.txt
    echo "Using runner/CMakeLists_${FLAVOR}.txt"
fi

if [ -f "windows/runner/Runner_${FLAVOR}.rc" ]; then
    cp "windows/runner/Runner_${FLAVOR}.rc" windows/runner/Runner.rc
    echo "Using Runner_${FLAVOR}.rc"
fi

# Build the Flutter app
echo "Building Flutter app..."
flutter build windows --dart-define "FLAVOR=$FLAVOR" --dart-define "CHANNEL=$CHANNEL" "--$MODE"

BUILD_RESULT=$?

# Restore original files
if [ -f "windows/CMakeLists.txt.backup" ]; then
    cp windows/CMakeLists.txt.backup windows/CMakeLists.txt
    cp windows/runner/CMakeLists.txt.backup windows/runner/CMakeLists.txt
    cp windows/runner/Runner.rc.backup windows/runner/Runner.rc
    echo "Restored original files"
fi

if [ $BUILD_RESULT -eq 0 ]; then
    echo ""
    echo "✅ Successfully built $FLAVOR flavor for Windows!"
    echo "Executable location: build/windows/runner/$MODE/"
else
    echo ""
    echo "❌ Failed to build $FLAVOR flavor for Windows!"
fi

exit $BUILD_RESULT
