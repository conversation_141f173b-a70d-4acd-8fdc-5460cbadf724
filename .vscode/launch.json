{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "gp_h5",
            "request": "launch",
            "type": "dart",
            "args": [
                    "--dart-define",
                    "FLAVOR=gp",
                    "--dart-define",
                    "CHANNEL=test"
            ]
        },
        {
            "name": "gp_h5 (pre mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release",
            "args": [
                "--dart-define",
                "FLAVOR=gp",
                "--dart-define",
                "CHANNEL=pre"
            ]
        },
        {
            "name": "gp_h5 (production mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release",
            "args": [
                "--dart-define",
                "FLAVOR=gp",
                "--dart-define",
                "CHANNEL=prod"
            ]
        },
        {
            "name": "yhxt_h5",
            "request": "launch",
            "type": "dart",
            "args": [
                "--dart-define",
                "FLAVOR=yhxt",
                "--dart-define",
                "CHANNEL=test"
            ]
        },
        {
            "name": "yhxt_h5 (pre mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release",
            "args": [
                "--dart-define",
                "FLAVOR=yhxt",
                "--dart-define",
                "CHANNEL=pre"
            ]
        },
        {
            "name": "yhxt_h5 (production mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release",
            "args": [
                "--dart-define",
                "FLAVOR=yhxt",
                "--dart-define",
                "CHANNEL=prod"
            ]
        },
        {
            "name": "bszb_h5",
            "request": "launch",
            "type": "dart",
            "args": [
                "--dart-define",
                "FLAVOR=bszb",
                "--dart-define",
                "CHANNEL=test"
            ]
        },
        {
            "name": "bszb_h5 (pre mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release",
            "args": [
                "--dart-define",
                "FLAVOR=bszb",
                "--dart-define",
                "CHANNEL=pre"
            ]
        },
        {
            "name": "bszb_h5 (production mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release",
            "args": [
                "--dart-define",
                "FLAVOR=bszb",
                "--dart-define",
                "CHANNEL=prod"
            ]
        },
        {
            "name": "dyzb_h5",
            "request": "launch",
            "type": "dart",
            "args": [
                "--dart-define",
                "FLAVOR=dyzb",
                "--dart-define",
                "CHANNEL=test"
            ]
        },
        {
            "name": "dyzb_h5 (pre mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release",
            "args": [
                "--dart-define",
                "FLAVOR=dyzb",
                "--dart-define",
                "CHANNEL=pre"
            ]
        },
        {
            "name": "dyzb_h5 (production mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release",
            "args": [
                "--dart-define",
                "FLAVOR=dyzb",
                "--dart-define",
                "CHANNEL=prod"
            ]
        },
            
    ]
}