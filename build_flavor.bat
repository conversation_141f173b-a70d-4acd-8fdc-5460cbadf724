@echo off
setlocal enabledelayedexpansion

REM Windows batch script to build Flutter app with specific flavor
REM Usage: build_flavor.bat <flavor> [mode] [channel]

if "%1"=="" (
    echo Usage: build_flavor.bat ^<flavor^> [mode] [channel]
    echo Available flavors: gp, yhxt, bszb, dyzb
    echo Available modes: debug, profile, release ^(default: release^)
    echo Available channels: test, pre, prod ^(default: prod^)
    exit /b 1
)

set FLAVOR=%1
set MODE=%2
set CHANNEL=%3

if "%MODE%"=="" set MODE=release
if "%CHANNEL%"=="" set CHANNEL=prod

REM Validate flavor
if not "%FLAVOR%"=="gp" if not "%FLAVOR%"=="yhxt" if not "%FLAVOR%"=="bszb" if not "%FLAVOR%"=="dyzb" (
    echo Invalid flavor: %FLAVOR%
    echo Available flavors: gp, yhxt, bszb, dyzb
    exit /b 1
)

echo Building Flutter app for flavor: %FLAVOR%, mode: %MODE%, channel: %CHANNEL%

REM Backup original files
if exist windows\CMakeLists.txt.backup goto skip_backup
copy windows\CMakeLists.txt windows\CMakeLists.txt.backup >nul
copy windows\runner\CMakeLists.txt windows\runner\CMakeLists.txt.backup >nul
copy windows\runner\Runner.rc windows\runner\Runner.rc.backup >nul
echo Backed up original files
:skip_backup

REM Copy flavor-specific files
if exist windows\CMakeLists_%FLAVOR%.txt (
    copy windows\CMakeLists_%FLAVOR%.txt windows\CMakeLists.txt >nul
    echo Using CMakeLists_%FLAVOR%.txt
)

if exist windows\runner\CMakeLists_%FLAVOR%.txt (
    copy windows\runner\CMakeLists_%FLAVOR%.txt windows\runner\CMakeLists.txt >nul
    echo Using runner\CMakeLists_%FLAVOR%.txt
)

if exist windows\runner\Runner_%FLAVOR%.rc (
    copy windows\runner\Runner_%FLAVOR%.rc windows\runner\Runner.rc >nul
    echo Using Runner_%FLAVOR%.rc
)

REM Build the Flutter app
echo Building Flutter app...
flutter build windows --dart-define FLAVOR=%FLAVOR% --dart-define CHANNEL=%CHANNEL% --%MODE%

set BUILD_RESULT=%ERRORLEVEL%

REM Restore original files
if exist windows\CMakeLists.txt.backup (
    copy windows\CMakeLists.txt.backup windows\CMakeLists.txt >nul
    copy windows\runner\CMakeLists.txt.backup windows\runner\CMakeLists.txt >nul
    copy windows\runner\Runner.rc.backup windows\runner\Runner.rc >nul
    echo Restored original files
)

if %BUILD_RESULT%==0 (
    echo.
    echo ✅ Successfully built %FLAVOR% flavor for Windows!
    echo Executable location: build\windows\runner\%MODE%\
) else (
    echo.
    echo ❌ Failed to build %FLAVOR% flavor for Windows!
)

exit /b %BUILD_RESULT%
