import 'package:gp_h5/flavorss.dart';
import 'package:gp_h5/services/oss_service.dart';

/// Defines the flavor of the app
enum Flavor {
  gp, // 测试环境
  yhxt, // 沅和信投 (生产-
  bszb, // 宝石资本 (生产-租户站)
  dyzb, // 德盈资本 (生产-租户站)
  rsyp, // 荣顺优配 (生产-演示站)
}

/// Configuration for the app based on flavor

// app_config.dart

class AppConfig {
  final String appName;
  final String siteId;
  final String appId;

  static AppConfig? _instance;

  const AppConfig({
    required this.appName,
    required this.siteId,
    required this.appId,
  });

  /// Initializes the singleton instance with the provided configuration.
  static void initialize(AppConfig config) {
    _instance = config;
  }

  /// Provides access to the singleton instance.
  ///
  /// Throws an assertion error if accessed before initialization.
  static AppConfig get instance {
    assert(
      _instance != null,
      'AppConfig must be initialized before accessing instance',
    );
    return _instance!;
  }

  Future<Map<String, dynamic>> getOssList() async {
    return await OssService().fetchOssData(siteId: siteId, channel: F.channel);
  }
}
