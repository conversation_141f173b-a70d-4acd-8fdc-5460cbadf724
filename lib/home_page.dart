import 'package:flutter/material.dart';
import 'package:gp_h5/config/flavors/app_config.dart';
import 'package:webview_flutter/webview_flutter.dart';

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key});

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
 WebViewController? controller;
  bool isLoading = true;
  String? errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }

  Future<void> _initializeWebView() async {
    try {
      // Use the OSS service
      final ossData = await AppConfig.instance.getOssList();
      
      if (ossData.isEmpty || ossData['success'] == false) {
        setState(() {
          errorMessage = 'No URLs available from getOssList';
          isLoading = false;
        });
        return;
      }

      controller = WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..setNavigationDelegate(
          NavigationDelegate(
            onProgress: (int progress) {
              // Update loading progress if needed
            },
            onPageStarted: (String url) {
              setState(() {
                isLoading = true;
                errorMessage = null;
              });
            },
            onPageFinished: (String url) async {
                 // Set localStorage key
                  setState(() {
                isLoading = false;
              });
          String jsSetLocalStorage = """
            localStorage.setItem('isNotFirst', 'true');
          """;
          
          await controller?.runJavaScript(jsSetLocalStorage);

             
            },
            onHttpError: (HttpResponseError error) {
              setState(() {
                errorMessage = 'HTTP Error: ${error.response?.statusCode}';
                isLoading = false;
              });
            },
            onWebResourceError: (WebResourceError error) {
              setState(() {
                errorMessage = 'Web Resource Error: ${error.description}';
                isLoading = false;
              });
            },
          ),
        )
        ..loadRequest(Uri.parse(ossData['fastestApiUrl'] ?? 'https://pc.${ossData['data'][0]}'));
    } catch (e) {
      setState(() {
        errorMessage = 'Error initializing WebView: $e';
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          if (errorMessage != null)
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
                  const SizedBox(height: 16),
                  Text(
                    'Error Loading Page',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 32),
                    child: Text(
                      errorMessage!,
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _initializeWebView,
                    child: const Text('Retry'),
                  ),
                ],
              ),
            )
          else
            WebViewWidget(controller: controller ?? WebViewController()),
          if (isLoading) const Center(child: CircularProgressIndicator()),
        ],
      ),
    );
  }
}