import 'package:dio/dio.dart';

import 'dart:developer' as developer;

import 'package:flutter/foundation.dart';

void debugger(String message) {
  if (kDebugMode) {
  developer.log(message, name: 'OssService');
  }
}

class OssService {
  static final OssService _instance = OssService._internal();
  factory OssService() => _instance;
  OssService._internal();

  final Dio _dio = Dio();

  /// Returns all OSS URLs for the app configuration based on site ID and environment
  List<String> getOssList({String? siteId, String? channel}) {
    final regions = [
      {'prefix': 'rs-1337543130', 'region': 'ap-shanghai'},
      {'prefix': 'bj-1337543130', 'region': 'ap-beijing'},
      {'prefix': 'gz-1337543130', 'region': 'ap-guangzhou'},
      {'prefix': 'cq-1337543130', 'region': 'ap-chongqing'},
      {'prefix': 'xg-1337543130', 'region': 'ap-hongkong'},
    ];

    return regions.map((region) {
      final prefix = region['prefix'];
      final regionName = region['region'];
      return 'https://$prefix.cos.$regionName.myqcloud.com/${channel ?? 'test'}/${siteId ?? '1'}/app_api.json';
    }).toList();
  }

  /// Makes an HTTP request to the first working OSS URL and returns the response with fastest API URL
  Future<Map<String, dynamic>> fetchOssData({
    String? siteId,
    String? channel,
  }) async {
    final urls = getOssList(siteId: siteId, channel: channel);

    Response<dynamic>? response;
    String? usedUrl;
    final triedUrls = <String>[];
    String? fastestApiUrl;

    // Try each OSS URL until one works
    for (final candidate in urls) {
      triedUrls.add(candidate);
      try {
        debugger('DEBUG: Trying OSS URL: $candidate');
        final res = await _dio.get(candidate);
        final code = res.statusCode ?? 0;
        if (code >= 200 && code < 300 && res.data != null) {
          response = res;
          usedUrl = candidate;
          debugger('DEBUG: OSS URL succeeded: $candidate with status $code');
          
          // Try to extract URLs and find fastest one from this response
          try {
            final data = res.data;
            List<String> urlList = [];

            // Extract URLs from the response data
            if (data is Map) {
              debugger('DEBUG: Data is Map, keys: ${data.keys}');
              data.forEach((key, value) {
                debugger(
                  'DEBUG: Checking key: $key, value: $value, type: ${value.runtimeType}',
                );
                if (value is String && _isValidUrl(value)) {
                  debugger('DEBUG: Valid URL found: $value');
                  urlList.add(value);
                }
              });
            } else if (data is List) {
              debugger('DEBUG: Data is List, length: ${data.length}');
              for (int i = 0; i < data.length; i++) {
                var item = data[i];
                debugger('DEBUG: Checking item[$i]: $item, type: ${item.runtimeType}');
                if (item is String && _isValidUrl(item)) {
                  debugger('DEBUG: Valid URL found: $item');
                  urlList.add(item);
                }
              }
            }

            debugger('DEBUG: Extracted URL list: $urlList');

            if (urlList.isNotEmpty) {
              fastestApiUrl = await _findFastestUrl(urlList);
              debugger('DEBUG: Fastest URL result: $fastestApiUrl');
              
              // If we found a fastest URL, we can break out of the OSS URL loop
              if (fastestApiUrl != null) {
                break;
              }
            } else {
              debugger('DEBUG: No valid URLs found in response data');
            }
          } catch (e) {
            debugger('DEBUG: Error processing response data: $e');
            // Continue to next OSS URL if this one fails
            continue;
          }
        } else {
          debugger('DEBUG: OSS URL non-success: $candidate with status $code');
        }
      } catch (e) {
        debugger('DEBUG: Error fetching OSS URL $candidate: $e');
        // Continue to next OSS URL if this one fails
        continue;
      }
    }

    if (response == null || usedUrl == null) {
      return {
        'success': false,
        'url': urls.isNotEmpty ? urls.first : null,
        'error': 'All OSS URLs failed',
        'data': null,
        'headers': null,
        'fastestApiUrl': null,
        'triedUrls': triedUrls,
      };
    }

    return {
      'success': true,
      'url': usedUrl,
      'statusCode': response.statusCode,
      'data': response.data,
      'headers': response.headers.map,
      'fastestApiUrl': fastestApiUrl,
      'triedUrls': triedUrls,
    };
  }

  /// Helper method to find the fastest URL from a list
  Future<String?> _findFastestUrl(List<String> urls) async {
    debugger('DEBUG: _findFastestUrl called with URLs: $urls');

    // Set reasonable timeouts
    _dio.options.connectTimeout = const Duration(seconds: 5);
    _dio.options.receiveTimeout = const Duration(seconds: 10);

    try {
      // Create a list of futures for concurrent requests
      final futures = urls.map((url) async {
        debugger('DEBUG: Testing URL: $url');
        final stopwatch = Stopwatch()..start();

        // Add scheme if not present
        String fullUrl = url;
        if (!url.startsWith('http://') && !url.startsWith('https://')) {
          fullUrl = 'https://pc.$url';
        }
        debugger('DEBUG: Full URL for testing: $fullUrl');

        try {
          final response = await _dio.get(fullUrl);
          stopwatch.stop();
          debugger('DEBUG: Response for $url: status ${response.statusCode}');

          if (response.statusCode == 200) {
            return {
              'url': url, // Keep original URL for return
              'responseTime': stopwatch.elapsedMilliseconds,
              'success': true,
            };
          } else {
            // Non-200 status code
            stopwatch.stop();
            return {
              'url': url,
              'responseTime': stopwatch.elapsedMilliseconds,
              'success': false,
              'error': 'HTTP ${response.statusCode}',
            };
          }
        } catch (e) {
          stopwatch.stop();
          debugger('DEBUG: Error testing $url: $e');
          return {
            'url': url,
            'responseTime': stopwatch.elapsedMilliseconds,
            'success': false,
            'error': e.toString(),
          };
        }
      }).toList();

      debugger('DEBUG: Waiting for all futures to complete...');
      // Wait for all requests to complete
      final results = await Future.wait(futures);
      debugger('DEBUG: All futures completed. Results: $results');

      // Filter successful results and find the fastest one
      final successfulResults = results
          .where((result) => result != null && result['success'] == true)
          .toList();

      debugger('DEBUG: Successful results: $successfulResults');

      if (successfulResults.isEmpty) {
        debugger('DEBUG: No successful requests found');
        return null; // No successful requests
      }

      // Sort by response time and get the fastest
      successfulResults.sort(
        (a, b) =>
            (a!['responseTime'] as int).compareTo(b!['responseTime'] as int),
      );

      final fastestUrl = successfulResults.first!['url'] as String;
      debugger('DEBUG: Fastest URL found: $fastestUrl');

      // Add "https://pc." prefix to the fastest URL
      if (fastestUrl.startsWith('http://') ||
          fastestUrl.startsWith('https://')) {
        final uri = Uri.parse(fastestUrl);
        final result = 'https://pc.${uri.host}';
        debugger('DEBUG: Final result with scheme: $result');
        return result;
      } else {
        // For plain domains like "gpnow.xyz"
        final result = 'https://pc.$fastestUrl';
        debugger('DEBUG: Final result without scheme: $result');
        return result;
      }
    } catch (e) {
      debugger('DEBUG: Error in _findFastestUrl: $e');
      return null;
    }
  }

  /// Helper method to check if a string is a valid URL or domain
  bool _isValidUrl(String url) {
    debugger('DEBUG: _isValidUrl called with: "$url"');

    try {
      // If it already has a scheme, validate as full URL
      if (url.startsWith('http://') || url.startsWith('https://')) {
        final uri = Uri.parse(url);
        final result = uri.hasScheme && uri.hasAuthority;
        debugger('DEBUG: Full URL validation result: $result');
        return result;
      }

      // If it's just a domain (like "gpnow.xyz"), consider it valid
      // Check if it looks like a domain (contains dots, no spaces, etc.)
      if (url.contains('.') && !url.contains(' ') && !url.startsWith('.')) {
        debugger('DEBUG: Domain validation result: true');
        return true;
      }

      debugger('DEBUG: Domain validation result: false');
      return false;
    } catch (e) {
      debugger('DEBUG: Error in _isValidUrl: $e');
      return false;
    }
  }

  /// Get the fastest API URL directly (simplified method)
  Future<String?> getFastestApiUrl({String? siteId, String? channel}) async {
    final ossData = await fetchOssData(siteId: siteId, channel: channel);
    return ossData['fastestApiUrl'];
  }

  /// Get OSS data without finding fastest URL (for cases where you just need the data)
  Future<Map<String, dynamic>> getOssDataOnly({
    String? siteId,
    String? channel,
  }) async {
    final urls = getOssList(siteId: siteId, channel: channel);
    final url = urls.first;

    try {
      final response = await _dio.get(url);
      return {
        'success': true,
        'url': url,
        'statusCode': response.statusCode,
        'data': response.data,
        'headers': response.headers.map,
      };
    } catch (e) {
      return {
        'success': false,
        'url': url,
        'error': e.toString(),
        'data': null,
      };
    }
  }
}
