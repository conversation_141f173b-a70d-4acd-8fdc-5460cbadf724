import 'package:gp_h5/config/flavors/bszb_config.dart' show setupBSZBConfig;
import 'package:gp_h5/config/flavors/dyzb_config.dart' show setupDYZBConfig;
import 'package:gp_h5/config/flavors/gp_config.dart';
import 'package:gp_h5/config/flavors/yhxt_config.dart' show setupYHXTConfig;

/// Enum representing different app flavors/variants
/// - [gp]: GP Stock app flavor
/// - [yhxt]: YHXT app flavor
/// - [bszb]: BSZB app flavor
/// - [dyzb]: DYZB app flavor
/// - [rsyp]: RSYP app flavor

enum Flavor {
  gp,
  yhxt,
  bszb,
  dyzb,
}

class F {
  /// Currently active app flavor that determines which configuration to use
  static late final Flavor appFlavor;

  /// Debug mode flag used to determine which environment URLs to use
  static const channel = String.fromEnvironment('CHANNEL', defaultValue: 'test');

  /// Get the name of current flavor as a string
  static String get name => appFlavor.name;

  /// Sets up the app configuration based on the current [appFlavor]
  /// This initializes environment-specific settings like URLs and assets
  static void setupConfig() => switch (appFlavor) {
        Flavor.gp => setupGPConfig(),
        Flavor.yhxt => setupYHXTConfig(),
        Flavor.bszb => setupBSZBConfig(),
        Flavor.dyzb => setupDYZBConfig(),
      };
}

