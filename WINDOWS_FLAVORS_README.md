# Windows Flavors Configuration

This document explains how to build and configure different flavors of the Flutter app for Windows with proper icons and app names.

**Note:** Windows builds can only be performed on Windows machines. The build scripts will work on any platform, but the actual Flutter build command will only succeed on Windows.

## Available Flavors

| Flavor | App Name | Executable Name | Icon |
|--------|----------|----------------|------|
| `gp` | GP Stock | gp_stock.exe | windows/runner/resources/gp/icon.ico |
| `yhxt` | 沅和信投 | yhxt_stock.exe | windows/runner/resources/yhxt/icon.ico |
| `bszb` | 宝石资本 | bszb_capital.exe | windows/runner/resources/bszb/icon.ico |
| `dyzb` | 德盈资本 | dyzb_capital.exe | windows/runner/resources/dyzb/icon.ico |

## Quick Start

### Method 1: Using Build Scripts (Recommended)

#### Windows (Command Prompt/PowerShell)
```cmd
# Build release version for GP flavor
build_flavor.bat gp

# Build debug version for BSZB flavor with test channel
build_flavor.bat bszb debug test

# Build profile version for DYZB flavor with pre channel
build_flavor.bat dyzb profile pre
```

#### macOS/Linux (Terminal)
```bash
# Build release version for GP flavor
./build_flavor.sh gp

# Build debug version for BSZB flavor with test channel
./build_flavor.sh bszb debug test

# Build profile version for DYZB flavor with pre channel
./build_flavor.sh dyzb profile pre
```

### Method 2: Using Python Script
```bash
# Build release version for GP flavor
python3 scripts/build_windows_flavor.py gp

# Build debug version for BSZB flavor with test channel
python3 scripts/build_windows_flavor.py bszb debug test
```

### Method 3: Manual Flutter Commands
```bash
# You need to manually copy flavor-specific files before building
cp windows/CMakeLists_gp.txt windows/CMakeLists.txt
cp windows/runner/CMakeLists_gp.txt windows/runner/CMakeLists.txt
cp windows/runner/Runner_gp.rc windows/runner/Runner.rc

# Then build (note: no --flavor flag, use --dart-define instead)
flutter build windows --dart-define FLAVOR=gp --dart-define CHANNEL=prod

# Don't forget to restore original files afterward
```

## Build Parameters

### Flavors
- `gp` - GP Stock application
- `yhxt` - 沅和信投 application  
- `bszb` - 宝石资本 application
- `dyzb` - 德盈资本 application

### Build Modes
- `debug` - Debug build with debugging symbols
- `profile` - Profile build for performance testing
- `release` - Release build for production (default)

### Channels
- `test` - Test environment
- `pre` - Pre-production environment
- `prod` - Production environment (default)

## File Structure

```
windows/
├── CMakeLists.txt                 # Main CMake configuration
├── CMakeLists_gp.txt             # GP flavor CMake config
├── CMakeLists_yhxt.txt           # YHXT flavor CMake config
├── CMakeLists_bszb.txt           # BSZB flavor CMake config
├── CMakeLists_dyzb.txt           # DYZB flavor CMake config
└── runner/
    ├── CMakeLists.txt            # Runner CMake configuration
    ├── CMakeLists_gp.txt         # GP flavor runner config
    ├── CMakeLists_yhxt.txt       # YHXT flavor runner config
    ├── CMakeLists_bszb.txt       # BSZB flavor runner config
    ├── CMakeLists_dyzb.txt       # DYZB flavor runner config
    ├── Runner.rc                 # Main resource file
    ├── Runner_gp.rc              # GP flavor resource file
    ├── Runner_yhxt.rc            # YHXT flavor resource file
    ├── Runner_bszb.rc            # BSZB flavor resource file
    ├── Runner_dyzb.rc            # DYZB flavor resource file
    └── resources/
        ├── gp/icon.ico           # GP flavor icon
        ├── yhxt/icon.ico         # YHXT flavor icon
        ├── bszb/icon.ico         # BSZB flavor icon
        └── dyzb/icon.ico         # DYZB flavor icon
```

## How It Works

1. **Flavor-Specific Resource Files**: Each flavor has its own `.rc` file with the correct icon path and app metadata
2. **Dynamic CMake Configuration**: CMakeLists.txt files are swapped during build to use the correct executable name
3. **Automatic Backup/Restore**: Build scripts automatically backup and restore original files
4. **Icon Management**: Each flavor uses its own icon from the resources folder

## Adding New Flavors

1. Add the new flavor to `lib/flavors.dart`
2. Create the icon file in `windows/runner/resources/<flavor_name>/icon.ico`
3. Update `scripts/generate_windows_flavors.py` with the new flavor configuration
4. Run the generation script: `python3 scripts/generate_windows_flavors.py`
5. Update this README with the new flavor information

## Troubleshooting

### Build Fails
- Ensure all icon files exist in the correct locations
- Check that Flutter is properly installed and in PATH
- Verify that Visual Studio Build Tools are installed

### Wrong Icon/Name
- Check that the correct flavor-specific files were generated
- Verify the icon file exists and is a valid .ico file
- Ensure the build script is using the correct flavor parameter

### File Permission Issues
- On macOS/Linux, ensure the shell script is executable: `chmod +x build_flavor.sh`
- On Windows, run Command Prompt as Administrator if needed

## Output Locations

Built executables will be located at:
- Debug: `build/windows/runner/debug/<executable_name>.exe`
- Profile: `build/windows/runner/profile/<executable_name>.exe`  
- Release: `build/windows/runner/release/<executable_name>.exe`

## VS Code Integration

The project includes VS Code launch configurations for each flavor. You can run/debug specific flavors directly from VS Code using the configurations in `.vscode/launch.json`.
