#!/usr/bin/env dart

/// Test script to verify flavor configuration
/// Usage: dart test_flavors.dart

import 'dart:io';

void main() {
  print('Testing Flutter Flavor Configuration for Windows\n');
  
  // Test each flavor
  final flavors = ['gp', 'yhxt', 'bszb', 'dyzb'];
  
  for (final flavor in flavors) {
    print('Testing flavor: $flavor');
    testFlavorFiles(flavor);
    print('');
  }
  
  print('✅ All flavor configuration tests completed!');
  print('\nTo test the actual app with different flavors, use:');
  for (final flavor in flavors) {
    print('  flutter run --dart-define FLAVOR=$flavor --dart-define CHANNEL=test');
  }
}

void testFlavorFiles(String flavor) {
  final files = [
    'windows/CMakeLists_$flavor.txt',
    'windows/runner/CMakeLists_$flavor.txt', 
    'windows/runner/Runner_$flavor.rc',
    'windows/runner/resources/$flavor/icon.ico',
  ];
  
  for (final file in files) {
    final exists = File(file).existsSync();
    final status = exists ? '✅' : '❌';
    print('  $status $file');
  }
}
