#!/bin/bash

FLAVOR="gp"
CHANNEL="test"

echo "Building macOS app for flavor: $FLAVOR..."
flutter build macos --flavor $FLAVOR --release --dart-define=CHANNEL=$CHANNEL

echo "Creating DMG installer..."
mkdir -p build/macos/installer

BUILD_DIR="build/macos/Build/Products/Release-$FLAVOR"
APP_PATH="$BUILD_DIR/Runner.app"

if [ ! -d "$APP_PATH" ]; then
    echo "Error: App not found at $APP_PATH"
    exit 1
fi

echo "Using app at: $APP_PATH"

# Create temp directory with just the app
TEMP_DIR=$(mktemp -d)
echo "Creating temporary directory: $TEMP_DIR"

# Copy the app
cp -R "$APP_PATH" "$TEMP_DIR/"

# Create Applications shortcut
ln -sf "/Applications" "$TEMP_DIR/Applications"

# Create a simple background image if it doesn't exist
if [ ! -f "build/macos/installer/background.png" ]; then
    echo "Creating background image..."
    magick -size 600x400 xc:white -fill "#f0f0f0" -draw "rectangle 0,0 600,400" build/macos/installer/background.png
fi

# Copy background to temp directory
cp "build/macos/installer/background.png" "$TEMP_DIR/"

# Verify the contents
echo "Contents of temp directory:"
ls -la "$TEMP_DIR"

# Try create-dmg to get the visual layout
echo "Creating DMG with create-dmg for visual layout..."
if create-dmg \
  --volname "$FLAVOR Installer" \
  --window-pos 200 120 \
  --window-size 600 400 \
  --icon-size 100 \
  --icon "Runner.app" 175 120 \
  --hide-extension "Runner.app" \
  --app-drop-link 425 120 \
  --background "build/macos/installer/background.png" \
  --no-internet-enable \
  "build/macos/installer/temp_${FLAVOR}_installer.dmg" \
  "$TEMP_DIR" 2>&1; then
  
  echo "create-dmg succeeded!"
  
  # Find the temporary DMG file
  TEMP_DMG=$(find build/macos/installer/ -name "rw.*temp_${FLAVOR}_installer.dmg" -type f | head -1)
  if [ -n "$TEMP_DMG" ]; then
    echo "Found temporary DMG: $TEMP_DMG"
    
    # Mount the temporary DMG to get the visual layout
    hdiutil attach "$TEMP_DMG" > /dev/null 2>&1
    
    # Find the mounted volume
    MOUNTED_VOLUME=$(hdiutil info | grep "temp_${FLAVOR}_installer" | tail -1 | awk '{print $NF}')
    
    if [ -n "$MOUNTED_VOLUME" ]; then
      echo "Found mounted volume: $MOUNTED_VOLUME"
      
      # Create a new DMG from the mounted volume with visual layout
      hdiutil create \
        -volname "$FLAVOR Installer" \
        -srcfolder "$MOUNTED_VOLUME" \
        -ov \
        -format UDZO \
        "build/macos/installer/${FLAVOR}_installer.dmg"
      
      # Unmount the temporary volume
      hdiutil detach "$MOUNTED_VOLUME" > /dev/null 2>&1
      
      echo "Created final DMG with visual layout!"
    else
      echo "Could not find mounted volume, using fallback"
      # Fallback to hdiutil
      hdiutil create \
        -volname "$FLAVOR Installer" \
        -srcfolder "$TEMP_DIR" \
        -ov \
        -format UDZO \
        "build/macos/installer/${FLAVOR}_installer.dmg"
    fi
    
    # Clean up temporary files
    rm -f "build/macos/installer/temp_${FLAVOR}_installer.dmg"
    rm -f build/macos/installer/rw.*temp_${FLAVOR}_installer.dmg
  fi
  
else
  echo "create-dmg failed, falling back to hdiutil..."
  
  # Fallback to hdiutil if create-dmg fails
  hdiutil create \
    -volname "$FLAVOR Installer" \
    -srcfolder "$TEMP_DIR" \
    -ov \
    -format UDZO \
    "build/macos/installer/${FLAVOR}_installer.dmg"
fi

# Clean up temp directory
rm -rf "$TEMP_DIR"

echo "DMG created successfully at build/macos/installer/${FLAVOR}_installer.dmg"
